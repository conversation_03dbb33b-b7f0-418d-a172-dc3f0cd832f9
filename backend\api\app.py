#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SZR管理后台API服务
"""

import os
import sys
import shutil
from datetime import datetime, timedelta
from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import JWTManager, create_access_token, jwt_required, get_jwt_identity
from werkzeug.security import generate_password_hash, check_password_hash
import json
import glob
import subprocess
import signal
import psutil
import threading
import time
import atexit

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

app = Flask(__name__)
CORS(app)

# 配置
app.config['SECRET_KEY'] = 'szr-admin-secret-key-2024'

# 使用绝对路径确保数据库文件位置固定，数据库文件位于backend根目录
db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'szr_admin.db')
app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['JWT_SECRET_KEY'] = 'jwt-secret-string'
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)

db = SQLAlchemy(app)
jwt = JWTManager(app)

# JWT错误处理
@jwt.unauthorized_loader
def unauthorized_callback(error_msg):
    return jsonify({'msg': 'Missing Authorization Header', 'error': '认证信息无效，请重新登录'}), 422

@jwt.invalid_token_loader
def invalid_token_callback(error_msg):
    return jsonify({'msg': 'Invalid token', 'error': '认证信息无效，请重新登录'}), 422

@jwt.expired_token_loader
def expired_token_callback(jwt_header, jwt_payload):
    return jsonify({'msg': 'Token has expired', 'error': '认证信息无效，请重新登录'}), 422

# 数据库模型
class User(db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    is_admin = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'is_admin': self.is_admin,
            'created_at': self.created_at.isoformat()
        }

class Avatar(db.Model):
    __tablename__ = 'avatars'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)
    display_name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    path = db.Column(db.String(255), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'display_name': self.display_name,
            'description': self.description,
            'path': self.path,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat()
        }

class LLMConfig(db.Model):
    __tablename__ = 'llm_configs'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)
    api_key = db.Column(db.String(255), nullable=False)
    base_url = db.Column(db.String(255), nullable=False)
    model_name = db.Column(db.String(100), nullable=False)
    is_active = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'api_key': self.api_key[:10] + '...' if len(self.api_key) > 10 else self.api_key,  # 隐藏部分API密钥
            'base_url': self.base_url,
            'model_name': self.model_name,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat()
        }
    
    def to_dict_full(self):
        return {
            'id': self.id,
            'name': self.name,
            'api_key': self.api_key,
            'base_url': self.base_url,
            'model_name': self.model_name,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat()
        }

class DigitalHumanConfig(db.Model):
    __tablename__ = 'digital_human_configs'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, default='默认配置')
    # 基础配置
    avatar_id = db.Column(db.String(100), nullable=True)
    llm_config_id = db.Column(db.Integer, nullable=True)
    model = db.Column(db.String(50), nullable=False, default='wav2lip')
    transport = db.Column(db.String(50), nullable=False, default='webrtc')
    listenport = db.Column(db.Integer, nullable=False, default=8010)
    tts = db.Column(db.String(50), nullable=False, default='edgetts')
    # 高级选项
    max_session = db.Column(db.Integer, nullable=False, default=1)
    batch_size = db.Column(db.Integer, nullable=False, default=16)
    multimodal_load = db.Column(db.Boolean, nullable=False, default=False)
    tts_server = db.Column(db.String(255), nullable=False, default='http://127.0.0.1:9880')
    push_url = db.Column(db.String(255), nullable=False, default='http://localhost:1985/rtc/v1/whip/?app=live&stream=livestream')
    # 语音参考配置
    ref_file = db.Column(db.String(500), nullable=True)  # 参考音频文件路径
    ref_text = db.Column(db.Text, nullable=True)  # 参考文本
    # 前端服务配置
    frontend_host = db.Column(db.String(100), nullable=False, default='127.0.0.1')
    frontend_page = db.Column(db.String(100), nullable=False, default='w.html')
    auto_open_browser = db.Column(db.Boolean, nullable=False, default=True)
    # 元数据
    is_default = db.Column(db.Boolean, nullable=False, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'avatar_id': self.avatar_id,
            'llm_config_id': self.llm_config_id,
            'model': self.model,
            'transport': self.transport,
            'listenport': self.listenport,
            'tts': self.tts,
            'max_session': self.max_session,
            'batch_size': self.batch_size,
            'multimodal_load': self.multimodal_load,
            'tts_server': self.tts_server,
            'push_url': self.push_url,
            'ref_file': self.ref_file,
            'ref_text': self.ref_text,
            'frontend_host': self.frontend_host,
            'frontend_page': self.frontend_page,
            'auto_open_browser': self.auto_open_browser,
            'is_default': self.is_default,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }


class UserAudioSetting(db.Model):
    __tablename__ = 'user_audio_settings'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    audio_directory = db.Column(db.String(500), nullable=True)  # 用户设置的音频目录路径
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关联用户
    user = db.relationship('User', backref=db.backref('audio_setting', uselist=False))

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'audio_directory': self.audio_directory,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

# API路由
@app.route('/api/auth/login', methods=['POST'])
def login():
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    
    if not username or not password:
        return jsonify({'error': '用户名和密码不能为空'}), 400
    
    user = User.query.filter_by(username=username).first()
    
    if user and user.check_password(password):
        access_token = create_access_token(identity=str(user.id))
        return jsonify({
            'access_token': access_token,
            'user': user.to_dict()
        })
    
    return jsonify({'error': '用户名或密码错误'}), 401

@app.route('/api/auth/register', methods=['POST'])
def register():
    data = request.get_json()
    username = data.get('username')
    email = data.get('email')
    password = data.get('password')
    
    if not username or not email or not password:
        return jsonify({'error': '所有字段都是必填的'}), 400
    
    if User.query.filter_by(username=username).first():
        return jsonify({'error': '用户名已存在'}), 400
    
    if User.query.filter_by(email=email).first():
        return jsonify({'error': '邮箱已存在'}), 400
    
    user = User(username=username, email=email)
    user.set_password(password)

    # 如果是第一个用户，设为管理员
    user_count = User.query.count()
    if user_count == 0:
        user.is_admin = True
    
    db.session.add(user)
    db.session.commit()
    
    access_token = create_access_token(identity=str(user.id))
    return jsonify({
        'access_token': access_token,
        'user': user.to_dict()
    }), 201

@app.route('/api/auth/me', methods=['GET'])
@jwt_required()
def get_current_user():
    user_id = int(get_jwt_identity())
    user = db.session.get(User, user_id)
    if user:
        return jsonify(user.to_dict())
    return jsonify({'error': '用户不存在'}), 404

# 用户管理API
@app.route('/api/users', methods=['GET'])
@jwt_required()
def get_users():
    users = User.query.all()
    return jsonify([user.to_dict() for user in users])

@app.route('/api/users', methods=['POST'])
@jwt_required()
def create_user():
    current_user_id = int(get_jwt_identity())
    current_user = db.session.get(User, current_user_id)

    if not current_user.is_admin:
        return jsonify({'error': '权限不足'}), 403

    data = request.get_json()
    username = data.get('username')
    email = data.get('email')
    password = data.get('password')
    is_admin = data.get('is_admin', False)

    if not username or not email or not password:
        return jsonify({'error': '用户名、邮箱和密码都是必填的'}), 400

    # 检查用户名是否已存在
    if User.query.filter_by(username=username).first():
        return jsonify({'error': '用户名已存在'}), 400

    # 检查邮箱是否已存在
    if User.query.filter_by(email=email).first():
        return jsonify({'error': '邮箱已存在'}), 400

    # 创建新用户
    user = User(username=username, email=email, is_admin=is_admin)
    user.set_password(password)

    db.session.add(user)
    db.session.commit()

    return jsonify(user.to_dict()), 201

@app.route('/api/users/<int:user_id>', methods=['PUT'])
@jwt_required()
def update_user(user_id):
    current_user_id = int(get_jwt_identity())
    current_user = db.session.get(User, current_user_id)

    if not current_user.is_admin:
        return jsonify({'error': '权限不足'}), 403

    user = db.session.get(User, user_id)
    if not user:
        return jsonify({'error': '用户不存在'}), 404

    data = request.get_json()

    # 更新用户名
    if 'username' in data:
        new_username = data['username']
        if new_username != user.username:
            # 检查新用户名是否已存在
            if User.query.filter_by(username=new_username).first():
                return jsonify({'error': '用户名已存在'}), 400
            user.username = new_username

    # 更新邮箱
    if 'email' in data:
        new_email = data['email']
        if new_email != user.email:
            # 检查新邮箱是否已存在
            if User.query.filter_by(email=new_email).first():
                return jsonify({'error': '邮箱已存在'}), 400
            user.email = new_email

    # 更新密码
    if 'password' in data and data['password']:
        user.set_password(data['password'])

    # 更新管理员权限
    if 'is_admin' in data:
        user.is_admin = data['is_admin']

    db.session.commit()
    return jsonify(user.to_dict())

@app.route('/api/users/<int:user_id>', methods=['DELETE'])
@jwt_required()
def delete_user(user_id):
    current_user_id = int(get_jwt_identity())
    current_user = db.session.get(User, current_user_id)

    if not current_user.is_admin:
        return jsonify({'error': '权限不足'}), 403

    if user_id == current_user_id:
        return jsonify({'error': '不能删除自己'}), 400

    user = db.session.get(User, user_id)
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    db.session.delete(user)
    db.session.commit()
    return jsonify({'message': '用户删除成功'})

# Avatar管理API
@app.route('/api/avatars', methods=['GET'])
@jwt_required()
def get_avatars():
    avatars = Avatar.query.all()
    return jsonify([avatar.to_dict() for avatar in avatars])

@app.route('/api/avatars/scan', methods=['POST'])
@jwt_required()
def scan_avatars():
    """扫描data/avatars文件夹并同步到数据库"""
    current_user_id = int(get_jwt_identity())
    current_user = db.session.get(User, current_user_id)

    if not current_user.is_admin:
        return jsonify({'error': '权限不足'}), 403

    avatars_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'avatars')

    if not os.path.exists(avatars_path):
        return jsonify({'error': 'avatars文件夹不存在'}), 404

    # 获取所有avatar文件夹
    avatar_folders = [d for d in os.listdir(avatars_path) if os.path.isdir(os.path.join(avatars_path, d))]

    # 获取数据库中所有的avatar记录
    db_avatars = Avatar.query.all()

    added_count = 0
    updated_count = 0
    removed_count = 0

    # 1. 添加新的avatar文件夹到数据库
    for folder_name in avatar_folders:
        existing_avatar = Avatar.query.filter_by(name=folder_name).first()
        if not existing_avatar:
            avatar = Avatar(
                name=folder_name,
                display_name=folder_name.replace('_', ' ').title(),
                description=f'Avatar {folder_name}',
                path=f'data/avatars/{folder_name}'
            )
            db.session.add(avatar)
            added_count += 1
        else:
            # 更新现有avatar的路径（以防路径格式有变化）
            if existing_avatar.path != f'data/avatars/{folder_name}':
                existing_avatar.path = f'data/avatars/{folder_name}'
                updated_count += 1

    # 2. 删除数据库中不存在的avatar文件夹对应的记录
    for db_avatar in db_avatars:
        if db_avatar.name not in avatar_folders:
            db.session.delete(db_avatar)
            removed_count += 1

    db.session.commit()

    # 构建返回消息
    message_parts = []
    if added_count > 0:
        message_parts.append(f'添加 {added_count} 个新avatar')
    if updated_count > 0:
        message_parts.append(f'更新 {updated_count} 个avatar')
    if removed_count > 0:
        message_parts.append(f'删除 {removed_count} 个不存在的avatar')

    if not message_parts:
        message = '数据库已是最新状态，无需更新'
    else:
        message = '成功' + '、'.join(message_parts)

    return jsonify({
        'message': message,
        'added': added_count,
        'updated': updated_count,
        'removed': removed_count
    })

@app.route('/api/avatars/<int:avatar_id>', methods=['PUT'])
@jwt_required()
def update_avatar(avatar_id):
    current_user_id = int(get_jwt_identity())
    current_user = db.session.get(User, current_user_id)

    if not current_user.is_admin:
        return jsonify({'error': '权限不足'}), 403

    avatar = db.session.get(Avatar, avatar_id)
    if not avatar:
        return jsonify({'error': 'Avatar不存在'}), 404

    data = request.get_json()
    if 'display_name' in data:
        avatar.display_name = data['display_name']
    if 'description' in data:
        avatar.description = data['description']
    if 'is_active' in data:
        avatar.is_active = data['is_active']

    db.session.commit()
    return jsonify(avatar.to_dict())

@app.route('/api/avatars/<int:avatar_id>', methods=['DELETE'])
@jwt_required()
def delete_avatar(avatar_id):
    current_user_id = int(get_jwt_identity())
    current_user = db.session.get(User, current_user_id)

    if not current_user.is_admin:
        return jsonify({'error': '权限不足'}), 403

    avatar = db.session.get(Avatar, avatar_id)
    if not avatar:
        return jsonify({'error': 'Avatar不存在'}), 404

    # 构建avatar文件夹路径
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    avatar_folder_path = os.path.join(project_root, 'data', 'avatars', avatar.name)

    try:
        # 删除数据库记录
        db.session.delete(avatar)
        db.session.commit()

        # 删除文件夹（如果存在）
        if os.path.exists(avatar_folder_path):
            shutil.rmtree(avatar_folder_path)
            return jsonify({'message': f'Avatar "{avatar.name}" 及其文件夹删除成功'})
        else:
            return jsonify({'message': f'Avatar "{avatar.name}" 删除成功（文件夹不存在）'})

    except Exception as e:
        # 如果删除文件夹失败，回滚数据库操作
        db.session.rollback()
        return jsonify({'error': f'删除失败: {str(e)}'}), 500

# LLM配置管理API
@app.route('/api/llm-configs', methods=['GET'])
@jwt_required()
def get_llm_configs():
    configs = LLMConfig.query.all()
    return jsonify([config.to_dict() for config in configs])

@app.route('/api/llm-configs', methods=['POST'])
@jwt_required()
def create_llm_config():
    current_user_id = int(get_jwt_identity())
    current_user = db.session.get(User, current_user_id)

    if not current_user.is_admin:
        return jsonify({'error': '权限不足'}), 403

    data = request.get_json()
    name = data.get('name')
    api_key = data.get('api_key')
    base_url = data.get('base_url')
    model_name = data.get('model_name')

    if not all([name, api_key, base_url, model_name]):
        return jsonify({'error': '所有字段都是必填的'}), 400

    if LLMConfig.query.filter_by(name=name).first():
        return jsonify({'error': '配置名称已存在'}), 400

    # 如果设置为激活状态，先取消其他配置的激活状态
    is_active = data.get('is_active', False)
    if is_active:
        LLMConfig.query.update({'is_active': False})

    config = LLMConfig(
        name=name,
        api_key=api_key,
        base_url=base_url,
        model_name=model_name,
        is_active=is_active
    )

    db.session.add(config)
    db.session.commit()
    return jsonify(config.to_dict()), 201

@app.route('/api/llm-configs/<int:config_id>', methods=['PUT'])
@jwt_required()
def update_llm_config(config_id):
    current_user_id = int(get_jwt_identity())
    current_user = db.session.get(User, current_user_id)

    if not current_user.is_admin:
        return jsonify({'error': '权限不足'}), 403

    config = db.session.get(LLMConfig, config_id)
    if not config:
        return jsonify({'error': '配置不存在'}), 404

    data = request.get_json()

    if 'name' in data:
        # 检查名称是否重复
        existing = LLMConfig.query.filter_by(name=data['name']).first()
        if existing and existing.id != config_id:
            return jsonify({'error': '配置名称已存在'}), 400
        config.name = data['name']

    if 'api_key' in data:
        config.api_key = data['api_key']
    if 'base_url' in data:
        config.base_url = data['base_url']
    if 'model_name' in data:
        config.model_name = data['model_name']

    # 如果设置为激活状态，先取消其他配置的激活状态
    if 'is_active' in data and data['is_active']:
        LLMConfig.query.filter(LLMConfig.id != config_id).update({'is_active': False})
        config.is_active = True
    elif 'is_active' in data:
        config.is_active = data['is_active']

    db.session.commit()
    return jsonify(config.to_dict())

@app.route('/api/llm-configs/<int:config_id>', methods=['DELETE'])
@jwt_required()
def delete_llm_config(config_id):
    current_user_id = int(get_jwt_identity())
    current_user = db.session.get(User, current_user_id)

    if not current_user.is_admin:
        return jsonify({'error': '权限不足'}), 403

    config = db.session.get(LLMConfig, config_id)
    if not config:
        return jsonify({'error': '配置不存在'}), 404

    db.session.delete(config)
    db.session.commit()
    return jsonify({'message': '配置删除成功'})

@app.route('/api/llm-configs/<int:config_id>/full', methods=['GET'])
@jwt_required()
def get_llm_config_full(config_id):
    """获取完整的LLM配置（包含完整API密钥）"""
    current_user_id = int(get_jwt_identity())
    current_user = db.session.get(User, current_user_id)

    if not current_user.is_admin:
        return jsonify({'error': '权限不足'}), 403

    config = db.session.get(LLMConfig, config_id)
    if not config:
        return jsonify({'error': '配置不存在'}), 404

    return jsonify(config.to_dict_full())

@app.route('/api/llm-configs/active', methods=['GET'])
def get_active_llm_config():
    """获取当前激活的LLM配置（供主项目调用）"""
    config = LLMConfig.query.filter_by(is_active=True).first()
    if config:
        return jsonify(config.to_dict_full())
    return jsonify({
        'error': '模型未配置，请先配置模型！',
        'message': '模型未配置，请先配置模型！',
        'configured': False
    }), 200

# 数字人配置管理API
@app.route('/api/digital-human/config', methods=['GET'])
@jwt_required()
def get_digital_human_config():
    """获取数字人启动配置"""
    # 获取默认配置或第一个配置
    config = DigitalHumanConfig.query.filter_by(is_default=True).first()
    if not config:
        config = DigitalHumanConfig.query.first()

    if config:
        return jsonify(config.to_dict())
    else:
        # 返回默认配置
        return jsonify({
            'avatar_id': '',
            'llm_config_id': None,
            'model': 'wav2lip',
            'transport': 'webrtc',
            'listenport': 8010,
            'tts': 'edgetts',
            'max_session': 1,
            'batch_size': 16,
            'multimodal_load': False,
            'tts_server': 'http://127.0.0.1:9880',
            'push_url': 'http://localhost:1985/rtc/v1/whip/?app=live&stream=livestream',
            'ref_file': '',
            'ref_text': '',
            'frontend_host': '127.0.0.1',
            'frontend_page': 'w.html',
            'auto_open_browser': True
        })

@app.route('/api/digital-human/config', methods=['POST'])
@jwt_required()
def save_digital_human_config():
    """保存数字人启动配置"""
    try:
        data = request.get_json()

        # 获取或创建默认配置
        config = DigitalHumanConfig.query.filter_by(is_default=True).first()
        if not config:
            config = DigitalHumanConfig(name='默认配置', is_default=True)
            db.session.add(config)

        # 更新配置
        if 'avatar_id' in data:
            config.avatar_id = data['avatar_id']
        if 'llm_config_id' in data:
            config.llm_config_id = data['llm_config_id']
        if 'model' in data:
            config.model = data['model']
        if 'transport' in data:
            config.transport = data['transport']
        if 'listenport' in data:
            config.listenport = data['listenport']
        if 'tts' in data:
            config.tts = data['tts']
        if 'max_session' in data:
            config.max_session = data['max_session']
        if 'batch_size' in data:
            config.batch_size = data['batch_size']
        if 'multimodal_load' in data:
            config.multimodal_load = data['multimodal_load']
        if 'tts_server' in data:
            config.tts_server = data['tts_server']
        if 'push_url' in data:
            config.push_url = data['push_url']
        if 'ref_file' in data:
            config.ref_file = data['ref_file']
        if 'ref_text' in data:
            config.ref_text = data['ref_text']
        if 'frontend_host' in data:
            config.frontend_host = data['frontend_host']
        if 'frontend_page' in data:
            config.frontend_page = data['frontend_page']
        if 'auto_open_browser' in data:
            config.auto_open_browser = data['auto_open_browser']

        config.updated_at = datetime.utcnow()
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '配置保存成功',
            'config': config.to_dict()
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'保存配置失败: {str(e)}'
        }), 500

# 数字人启动相关的全局变量
digital_human_processes = {
    'backend': None,
    'frontend': None
}

def cleanup_digital_human_processes():
    """清理数字人进程"""
    print("正在清理数字人进程...")

    # 停止后端服务
    if digital_human_processes['backend']:
        try:
            print(f"正在停止数字人后端服务，PID: {digital_human_processes['backend'].pid}")
            digital_human_processes['backend'].terminate()
            # 等待进程结束
            try:
                digital_human_processes['backend'].wait(timeout=10)
                print("数字人后端服务已正常停止")
            except subprocess.TimeoutExpired:
                print("数字人后端服务停止超时，强制杀死进程")
                digital_human_processes['backend'].kill()
                digital_human_processes['backend'].wait()
                print("数字人后端服务已强制停止")
            digital_human_processes['backend'] = None
        except Exception as e:
            print(f"停止数字人后端服务时出错: {e}")

    # 停止前端服务（实际上前端是静态文件，由后端提供）
    if digital_human_processes['frontend'] and digital_human_processes['frontend'] != digital_human_processes['backend']:
        try:
            print(f"正在停止数字人前端服务，PID: {digital_human_processes['frontend'].pid}")
            digital_human_processes['frontend'].terminate()
            try:
                digital_human_processes['frontend'].wait(timeout=5)
                print("数字人前端服务已正常停止")
            except subprocess.TimeoutExpired:
                print("数字人前端服务停止超时，强制杀死进程")
                digital_human_processes['frontend'].kill()
                digital_human_processes['frontend'].wait()
                print("数字人前端服务已强制停止")
            digital_human_processes['frontend'] = None
        except Exception as e:
            print(f"停止数字人前端服务时出错: {e}")

    print("数字人进程清理完成")

def signal_handler(signum, frame):
    """信号处理器"""
    print(f"接收到信号 {signum}，正在清理资源...")
    cleanup_digital_human_processes()
    sys.exit(0)

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
if hasattr(signal, 'SIGTERM'):
    signal.signal(signal.SIGTERM, signal_handler)  # 终止信号 (Unix/Linux)

# 在Windows上注册额外的信号处理
if os.name == 'nt':  # Windows
    if hasattr(signal, 'SIGBREAK'):
        signal.signal(signal.SIGBREAK, signal_handler)

# 注册退出时的清理函数
atexit.register(cleanup_digital_human_processes)

# 数字人启动API
@app.route('/api/digital-human/start', methods=['POST'])
@jwt_required()
def start_digital_human():
    """启动数字人前后端服务"""
    try:
        data = request.get_json()

        # 检查是否已有服务在运行
        if is_service_running('backend') or is_service_running('frontend'):
            return jsonify({
                'success': False,
                'message': '已有数字人服务在运行，请先停止现有服务'
            }), 400

        # 验证LLM配置
        active_llm = LLMConfig.query.filter_by(is_active=True).first()
        if not active_llm:
            return jsonify({
                'success': False,
                'message': '请先配置并激活一个LLM模型'
            }), 400

        # 验证Avatar配置
        avatar_id = data.get('avatar_id')
        if not avatar_id:
            return jsonify({
                'success': False,
                'message': '请选择一个人物形象'
            }), 400

        # 检查avatar文件是否存在
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        avatar_path = os.path.join(project_root, 'data', 'avatars', avatar_id)
        if not os.path.exists(avatar_path):
            return jsonify({
                'success': False,
                'message': f'人物形象 {avatar_id} 不存在，请检查data/avatars目录'
            }), 400

        # 构建启动命令
        backend_cmd = build_backend_command(data)

        # 启动后端服务
        backend_process = start_backend_service(backend_cmd, data)
        if not backend_process:
            return jsonify({
                'success': False,
                'message': '后端服务启动失败'
            }), 500

        # 等待后端服务启动
        time.sleep(3)

        # 启动前端服务（web3目录）
        frontend_process = start_frontend_service(data.get('listenport', 8010))

        # 构建前端URL
        frontend_host = data.get('frontend_host', '127.0.0.1')
        frontend_page = data.get('frontend_page', 'w.html')
        frontend_url = f"http://{frontend_host}:{data.get('listenport', 8010)}/{frontend_page}"

        return jsonify({
            'success': True,
            'message': '数字人服务启动成功',
            'backend_port': data.get('listenport', 8010),
            'backend_pid': backend_process.pid if backend_process else None,
            'frontend_url': frontend_url,
            'frontend_pid': frontend_process.pid if frontend_process else None
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'启动失败: {str(e)}'
        }), 500

@app.route('/api/digital-human/stop', methods=['POST'])
@jwt_required()
def stop_digital_human():
    """停止数字人服务"""
    try:
        # 记录当前运行的服务
        running_services = []
        if digital_human_processes['backend']:
            running_services.append('backend')
        if digital_human_processes['frontend'] and digital_human_processes['frontend'] != digital_human_processes['backend']:
            running_services.append('frontend')

        # 使用通用清理函数
        cleanup_digital_human_processes()

        return jsonify({
            'success': True,
            'message': f'已停止服务: {", ".join(running_services) if running_services else "无运行中的服务"}',
            'stopped_services': running_services
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'停止失败: {str(e)}'
        }), 500

@app.route('/api/digital-human/status', methods=['GET'])
@jwt_required()
def get_digital_human_status():
    """获取数字人服务状态"""
    try:
        backend_running = is_service_running('backend')
        frontend_running = is_service_running('frontend')

        backend_port = None
        frontend_url = None

        if backend_running and digital_human_processes['backend']:
            # 尝试从进程中获取端口信息
            backend_port = 8010  # 默认端口，实际应该从配置中获取
            # 使用默认前端主机地址，实际运行时会被正确的URL覆盖
            frontend_host = "127.0.0.1"  # 默认值，实际会被启动配置覆盖
            frontend_url = f"http://{frontend_host}:{backend_port}/w.html"

        return jsonify({
            'backend_running': backend_running,
            'backend_port': backend_port,
            'backend_pid': digital_human_processes['backend'].pid if digital_human_processes['backend'] else None,
            'frontend_running': frontend_running,
            'frontend_url': frontend_url,
            'frontend_pid': digital_human_processes['frontend'].pid if digital_human_processes['frontend'] else None
        })

    except Exception as e:
        return jsonify({
            'backend_running': False,
            'frontend_running': False,
            'backend_port': None,
            'frontend_url': None,
            'backend_pid': None,
            'frontend_pid': None
        })

def build_backend_command(config):
    """构建后端启动命令"""
    # 获取项目根目录
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

    cmd = [
        sys.executable,  # Python解释器
        os.path.join(project_root, 'app2.py'),
        '--transport', config.get('transport', 'webrtc'),
        '--model', config.get('model', 'wav2lip'),
        '--avatar_id', config.get('avatar_id', 'wav2lip256_avatar1'),  # 使用app2.py的默认值
        '--listenport', str(config.get('listenport', 8010)),
        '--tts', config.get('tts', 'edgetts'),
        '--max_session', str(config.get('max_session', 1)),
        '--batch_size', str(config.get('batch_size', 16))
    ]

    # 添加可选参数
    if config.get('multimodal_load'):
        cmd.append('--multimodal_load')

    # 只有当tts不是edgetts时才添加TTS_SERVER参数
    if config.get('tts_server') and config.get('tts') != 'edgetts':
        cmd.extend(['--TTS_SERVER', config.get('tts_server')])

    if config.get('push_url') and config.get('transport') != 'webrtc':
        cmd.extend(['--push_url', config.get('push_url')])

    # 添加语音参考参数
    if config.get('ref_file') and config.get('tts') != 'edgetts':
        cmd.extend(['--REF_FILE', config.get('ref_file')])

    if config.get('ref_text') and config.get('tts') != 'edgetts':
        cmd.extend(['--REF_TEXT', config.get('ref_text')])

    return cmd

def start_backend_service(cmd, data=None):
    """启动后端服务"""
    try:
        # 获取项目根目录作为工作目录
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

        print(f"项目根目录: {project_root}")
        print(f"启动命令: {' '.join(cmd)}")

        # 检查app2.py文件是否存在
        app2_path = os.path.join(project_root, 'app2.py')
        if not os.path.exists(app2_path):
            print(f"错误: app2.py文件不存在于 {app2_path}")
            return None

        # 更新config.js文件中的端口配置
        update_config_js(project_root, cmd, data)

        # 启动进程，使用文件记录输出
        log_file = os.path.join(project_root, 'digital_human_startup.log')
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write(f"启动命令: {' '.join(cmd)}\n")
            f.write(f"工作目录: {project_root}\n")
            f.write("=" * 50 + "\n")

        process = subprocess.Popen(
            cmd,
            cwd=project_root,
            stdout=open(log_file, 'a', encoding='utf-8'),
            stderr=subprocess.STDOUT,  # 将stderr重定向到stdout
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
        )

        # 等待更长时间让服务启动
        print(f"等待数字人服务启动，PID: {process.pid}")
        for i in range(30):  # 等待最多30秒
            time.sleep(1)
            if process.poll() is not None:
                # 进程已经退出，读取日志文件
                print(f"进程在第{i+1}秒时退出")
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        log_content = f.read()
                    print(f"启动日志:\n{log_content}")
                except Exception as e:
                    print(f"读取日志文件失败: {e}")
                return None

            # 检查端口是否开始监听
            try:
                import socket
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                # 使用localhost检查端口，因为服务绑定在0.0.0.0上
                result = sock.connect_ex(('127.0.0.1', int(cmd[cmd.index('--listenport') + 1])))
                sock.close()
                if result == 0:
                    print(f"端口已开始监听，服务启动成功")
                    break
            except:
                pass

            print(f"等待中... ({i+1}/30)")

        digital_human_processes['backend'] = process
        print(f"后端服务启动完成，PID: {process.pid}")
        print(f"启动日志保存在: {log_file}")

        # 自动打开浏览器
        if data and data.get('auto_open_browser', False):
            try:
                frontend_host = data.get('frontend_host', '127.0.0.1')
                frontend_page = data.get('frontend_page', 'w.html')
                port = int(cmd[cmd.index('--listenport') + 1]) if '--listenport' in cmd else 8010
                frontend_url = f"http://{frontend_host}:{port}/{frontend_page}"

                print(f"自动打开前端页面: {frontend_url}")
                import webbrowser
                webbrowser.open(frontend_url)
                print("浏览器已自动打开")
            except Exception as e:
                print(f"自动打开浏览器失败: {e}")

        return process

    except Exception as e:
        print(f"启动后端服务失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def update_config_js(project_root, cmd, data=None):
    """更新web3/config.js文件中的端口配置"""
    try:
        # 从启动命令中提取端口
        port = 8010  # 默认端口
        if '--listenport' in cmd:
            port_index = cmd.index('--listenport') + 1
            if port_index < len(cmd):
                port = cmd[port_index]

        # 从data中获取前端主机地址，如果没有则使用默认值
        frontend_host = '127.0.0.1'
        if data and 'frontend_host' in data:
            frontend_host = data['frontend_host']

        config_path = os.path.join(project_root, 'web3', 'config.js')

        # 读取现有配置
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 更新host配置
            import re
            content = re.sub(
                r'window\.host\s*=\s*["\'][^"\']*["\'];',
                f'window.host = "{frontend_host}:{port}";',
                content
            )

            # 写回文件
            with open(config_path, 'w', encoding='utf-8') as f:
                f.write(content)

            print(f"已更新config.js中的端口配置为: {frontend_host}:{port}")

    except Exception as e:
        print(f"更新config.js失败: {e}")

def start_frontend_service(port):
    """启动前端服务（实际上前端由后端提供）"""
    # 前端是静态文件，由后端app2.py提供
    # 这里只是为了保持接口一致性
    # port参数保留用于接口兼容性
    digital_human_processes['frontend'] = digital_human_processes['backend']
    print(f"前端服务将通过端口 {port} 提供")
    return digital_human_processes['backend']

def is_service_running(service_type):
    """检查服务是否在运行"""
    process = digital_human_processes.get(service_type)
    if process is None:
        return False

    try:
        # 检查进程是否还在运行
        return process.poll() is None
    except:
        return False

# 静态文件访问路由
@app.route('/api/avatars/<avatar_name>/image')
def get_avatar_image(avatar_name):
    """获取人物形象的预览图片"""
    try:
        # 构建图片路径
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        avatar_path = os.path.join(project_root, 'data', 'avatars', avatar_name)

        # 检查avatar文件夹是否存在
        if not os.path.exists(avatar_path):
            return jsonify({'error': '人物形象文件夹不存在'}), 404

        # 优先使用full_imgs中的图片，如果不存在则使用face_imgs中的图片
        image_file = '00000000.png'

        # 首先尝试full_imgs目录
        full_imgs_path = os.path.join(avatar_path, 'full_imgs')
        full_image_path = os.path.join(full_imgs_path, image_file)

        if os.path.exists(full_image_path):
            return send_from_directory(full_imgs_path, image_file)

        # 如果full_imgs不存在，尝试face_imgs目录
        face_imgs_path = os.path.join(avatar_path, 'face_imgs')
        face_image_path = os.path.join(face_imgs_path, image_file)

        if os.path.exists(face_image_path):
            return send_from_directory(face_imgs_path, image_file)

        # 如果都不存在，返回错误
        return jsonify({'error': '预览图片不存在'}), 404

    except Exception as e:
        return jsonify({'error': str(e)}), 500


# 音频文件管理API
@app.route('/api/audio/list', methods=['GET'])
@jwt_required()
def get_audio_list():
    """获取音频文件列表"""
    try:
        # 获取查询参数中的目录路径
        custom_path = request.args.get('path', '')

        # 构建音频文件夹路径
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

        if custom_path:
            # 使用自定义路径
            if os.path.isabs(custom_path):
                audio_path = custom_path
            else:
                audio_path = os.path.join(project_root, custom_path)
        else:
            # 使用默认路径
            audio_path = os.path.join(project_root, 'data', 'audio')

        # 检查文件夹是否存在
        if not os.path.exists(audio_path):
            return jsonify({
                'success': True,
                'audio_files': [],
                'total_count': 0,
                'current_path': audio_path,
                'message': f'目录不存在: {audio_path}'
            })

        # 检查是否为目录
        if not os.path.isdir(audio_path):
            return jsonify({
                'success': False,
                'message': f'指定的路径不是目录: {audio_path}'
            }), 400

        # 获取音频文件列表
        audio_files = []
        supported_formats = ['.wav', '.mp3', '.m4a', '.flac', '.aac', '.ogg']

        for filename in os.listdir(audio_path):
            file_path = os.path.join(audio_path, filename)
            if os.path.isfile(file_path):
                # 检查文件扩展名
                _, ext = os.path.splitext(filename)
                if ext.lower() in supported_formats:
                    # 获取文件信息
                    file_stat = os.stat(file_path)
                    file_size = file_stat.st_size
                    file_mtime = datetime.fromtimestamp(file_stat.st_mtime)

                    audio_files.append({
                        'filename': filename,
                        'size': file_size,
                        'size_mb': round(file_size / (1024 * 1024), 2),
                        'modified_time': file_mtime.strftime('%Y-%m-%d %H:%M:%S'),
                        'extension': ext.lower(),
                        'full_path': file_path
                    })

        # 按文件名排序
        audio_files.sort(key=lambda x: x['filename'])

        return jsonify({
            'success': True,
            'audio_files': audio_files,
            'total_count': len(audio_files),
            'current_path': audio_path
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取音频文件列表失败: {str(e)}'
        }), 500


@app.route('/api/audio/file/<filename>')
@jwt_required()
def get_audio_file(filename):
    """获取音频文件"""
    try:
        # 获取查询参数中的目录路径
        custom_path = request.args.get('path', '')

        # 构建音频文件路径
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

        if custom_path:
            # 使用自定义路径
            if os.path.isabs(custom_path):
                audio_path = custom_path
            else:
                audio_path = os.path.join(project_root, custom_path)
        else:
            # 使用默认路径
            audio_path = os.path.join(project_root, 'data', 'audio')

        file_path = os.path.join(audio_path, filename)

        # 检查文件是否存在
        if not os.path.exists(file_path):
            return jsonify({'error': '音频文件不存在'}), 404

        # 检查文件是否在指定目录内（安全检查）
        if not os.path.commonpath([audio_path, file_path]) == audio_path:
            return jsonify({'error': '非法的文件路径'}), 403

        # 返回音频文件
        return send_from_directory(audio_path, filename)

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/audio/path/<filename>')
@jwt_required()
def get_audio_file_path(filename):
    """获取音频文件的绝对路径"""
    try:
        # 构建音频文件路径
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        audio_path = os.path.join(project_root, 'data', 'audio')
        file_path = os.path.join(audio_path, filename)

        # 检查文件是否存在
        if not os.path.exists(file_path):
            return jsonify({'error': '音频文件不存在'}), 404

        # 检查文件是否在audio目录内（安全检查）
        if not os.path.commonpath([audio_path, file_path]) == audio_path:
            return jsonify({'error': '非法的文件路径'}), 403

        # 返回绝对路径（跨平台兼容）
        absolute_path = os.path.abspath(file_path)

        return jsonify({
            'success': True,
            'filename': filename,
            'absolute_path': absolute_path,
            'relative_path': os.path.join('data', 'audio', filename)
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/audio/browse', methods=['GET'])
@jwt_required()
def browse_directories():
    """浏览目录结构，用于选择音频文件目录"""
    try:
        print(f"Browse directories called with path: {request.args.get('path', '')}")
        # 获取查询参数中的目录路径
        path = request.args.get('path', '')

        # 构建目录路径
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

        if not path:
            # 如果没有指定路径，返回系统根目录或驱动器列表
            import platform
            if platform.system() == 'Windows':
                # Windows系统：返回所有驱动器
                import string
                drives = []
                for letter in string.ascii_uppercase:
                    drive_path = f"{letter}:\\"
                    if os.path.exists(drive_path):
                        drives.append({
                            'name': f"{letter}:",
                            'path': drive_path,
                            'relative_path': drive_path
                        })

                return jsonify({
                    'success': True,
                    'current_path': '计算机',
                    'current_relative_path': '',
                    'parent_path': None,
                    'parent_relative_path': None,
                    'directories': drives,
                    'files': [],
                    'total_directories': len(drives),
                    'total_files': 0,
                    'is_root': True
                })
            else:
                # Unix/Linux系统：从根目录开始
                browse_path = '/'
        elif os.path.isabs(path):
            browse_path = path
        else:
            # 相对路径基于项目根目录
            browse_path = os.path.join(project_root, path)

        # 检查目录是否存在
        if not os.path.exists(browse_path):
            return jsonify({
                'success': False,
                'message': f'目录不存在: {browse_path}'
            }), 404

        # 检查是否为目录
        if not os.path.isdir(browse_path):
            return jsonify({
                'success': False,
                'message': f'指定的路径不是目录: {browse_path}'
            }), 400

        # 获取目录内容
        directories = []
        files = []

        try:
            for item in os.listdir(browse_path):
                item_path = os.path.join(browse_path, item)

                if os.path.isdir(item_path):
                    directories.append({
                        'name': item,
                        'path': item_path,
                        'relative_path': item_path  # 使用绝对路径
                    })
                elif os.path.isfile(item_path):
                    # 只显示音频文件
                    _, ext = os.path.splitext(item)
                    if ext.lower() in ['.wav', '.mp3', '.m4a', '.flac', '.aac', '.ogg']:
                        file_stat = os.stat(item_path)
                        files.append({
                            'name': item,
                            'path': item_path,
                            'relative_path': item_path,  # 使用绝对路径
                            'size': file_stat.st_size,
                            'size_mb': round(file_stat.st_size / (1024 * 1024), 2),
                            'extension': ext.lower()
                        })
        except PermissionError:
            return jsonify({
                'success': False,
                'message': f'没有权限访问目录: {browse_path}'
            }), 403

        # 排序
        directories.sort(key=lambda x: x['name'].lower())
        files.sort(key=lambda x: x['name'].lower())

        # 获取父目录信息
        import platform
        parent_path = None
        if os.path.dirname(browse_path) != browse_path:
            parent_path = os.path.dirname(browse_path)
            # 如果父目录是驱动器根目录（如 C:\），则设置为空以返回驱动器列表
            if platform.system() == 'Windows' and len(parent_path) == 3 and parent_path.endswith(':\\'):
                if parent_path != browse_path:  # 不是当前就是驱动器根目录
                    parent_path = ''  # 返回驱动器列表

        return jsonify({
            'success': True,
            'current_path': browse_path,
            'current_relative_path': browse_path,  # 使用绝对路径
            'parent_path': parent_path,
            'parent_relative_path': parent_path if parent_path else '',
            'directories': directories,
            'files': files,
            'total_directories': len(directories),
            'total_files': len(files),
            'is_root': False
        })

    except Exception as e:
        print(f"Browse directories error: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'message': f'浏览目录失败: {str(e)}'
        }), 500


# 用户音频设置API
@app.route('/api/user/audio-setting', methods=['GET'])
@jwt_required()
def get_user_audio_setting():
    """获取当前用户的音频目录设置"""
    try:
        current_user_id = int(get_jwt_identity())

        # 查找用户的音频设置
        audio_setting = UserAudioSetting.query.filter_by(user_id=current_user_id).first()

        if audio_setting:
            return jsonify({
                'success': True,
                'audio_directory': audio_setting.audio_directory
            })
        else:
            return jsonify({
                'success': True,
                'audio_directory': None  # 使用默认目录
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取音频设置失败: {str(e)}'
        }), 500


@app.route('/api/user/audio-setting', methods=['POST'])
@jwt_required()
def save_user_audio_setting():
    """保存当前用户的音频目录设置"""
    try:
        current_user_id = int(get_jwt_identity())
        data = request.get_json()

        audio_directory = data.get('audio_directory', '').strip()

        # 查找现有设置
        audio_setting = UserAudioSetting.query.filter_by(user_id=current_user_id).first()

        if audio_setting:
            # 更新现有设置
            audio_setting.audio_directory = audio_directory if audio_directory else None
            audio_setting.updated_at = datetime.utcnow()
        else:
            # 创建新设置
            audio_setting = UserAudioSetting(
                user_id=current_user_id,
                audio_directory=audio_directory if audio_directory else None
            )
            db.session.add(audio_setting)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '音频目录设置已保存',
            'audio_directory': audio_setting.audio_directory
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'保存音频设置失败: {str(e)}'
        }), 500


# 自定义人物形象生成API
@app.route('/api/custom-avatar/generate', methods=['POST'])
@jwt_required()
def generate_custom_avatar():
    """生成自定义人物形象"""
    try:
        data = request.get_json()

        # 获取参数
        img_size = data.get('img_size', 96)
        avatar_id = data.get('avatar_id', 'wav2lip_avatar1')
        video_path = data.get('video_path', '')
        nosmooth = data.get('nosmooth', False)
        pads = data.get('pads', [0, 10, 0, 0])
        face_det_batch_size = data.get('face_det_batch_size', 16)

        # 验证必需参数
        if not avatar_id:
            return jsonify({'success': False, 'message': '人物形象ID不能为空'}), 400

        if not video_path:
            return jsonify({'success': False, 'message': '视频路径不能为空'}), 400

        # 获取项目根目录
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

        # 构建完整的视频路径
        if not os.path.isabs(video_path):
            full_video_path = os.path.join(project_root, video_path)
        else:
            full_video_path = video_path

        # 检查视频文件是否存在
        if not os.path.exists(full_video_path):
            return jsonify({'success': False, 'message': f'视频文件不存在: {full_video_path}'}), 400

        # 构建命令参数
        cmd = [
            'python',
            os.path.join(project_root, 'wav2lip', 'genavatar.py'),
            '--img_size', str(img_size),
            '--avatar_id', avatar_id,
            '--video_path', full_video_path,
            '--face_det_batch_size', str(face_det_batch_size)
        ]

        # 添加可选参数
        if nosmooth:
            cmd.append('--nosmooth')

        if pads and len(pads) == 4:
            cmd.extend(['--pads'] + [str(p) for p in pads])

        # 执行命令
        result = subprocess.run(
            cmd,
            cwd=project_root,
            capture_output=True,
            text=True,
            timeout=600  # 10分钟超时，与前端保持一致
        )

        # 检查执行结果
        if result.returncode == 0:
            # 生成成功，检查生成的文件
            avatar_path = os.path.join(project_root, 'data', 'avatars', avatar_id)
            full_imgs_path = os.path.join(avatar_path, 'full_imgs')
            face_imgs_path = os.path.join(avatar_path, 'face_imgs')
            coords_path = os.path.join(avatar_path, 'coords.pkl')

            # 统计生成的图片数量
            image_count = 0
            if os.path.exists(face_imgs_path):
                image_files = glob.glob(os.path.join(face_imgs_path, '*.png'))
                image_count = len(image_files)

            return jsonify({
                'success': True,
                'message': '人物形象生成成功',
                'avatar_id': avatar_id,
                'avatar_path': avatar_path,
                'image_count': image_count,
                'coords_file': coords_path if os.path.exists(coords_path) else None,
                'stdout': result.stdout,
                'stderr': result.stderr
            })
        else:
            # 生成失败
            error_message = result.stderr or result.stdout or '未知错误'
            return jsonify({
                'success': False,
                'message': f'生成失败: {error_message}',
                'stdout': result.stdout,
                'stderr': result.stderr
            }), 500

    except subprocess.TimeoutExpired:
        return jsonify({
            'success': False,
            'message': '生成超时，请检查视频文件大小和系统性能'
        }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'生成过程中发生错误: {str(e)}'
        }), 500


if __name__ == '__main__':
    with app.app_context():
        db.create_all()

    # 检查是否在测试环境中
    is_testing = 'test' in sys.argv[0].lower() or any('test' in arg.lower() for arg in sys.argv)
    debug_mode = not is_testing  # 测试时不使用调试模式

    print(f"启动API服务器 (调试模式: {debug_mode})")
    app.run(debug=debug_mode, port=5001, use_reloader=False)
