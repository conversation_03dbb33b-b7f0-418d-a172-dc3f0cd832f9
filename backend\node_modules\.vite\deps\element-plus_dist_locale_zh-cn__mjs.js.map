{"version": 3, "sources": ["../../element-plus/dist/locale/zh-cn.mjs"], "sourcesContent": ["/*! Element Plus v2.10.2 */\n\nvar zhCn = {\n  name: \"zh-cn\",\n  el: {\n    breadcrumb: {\n      label: \"\\u9762\\u5305\\u5C51\"\n    },\n    colorpicker: {\n      confirm: \"\\u786E\\u5B9A\",\n      clear: \"\\u6E05\\u7A7A\",\n      defaultLabel: \"\\u989C\\u8272\\u9009\\u62E9\\u5668\",\n      description: \"\\u5F53\\u524D\\u989C\\u8272 {color}\\uFF0C\\u6309 Enter \\u952E\\u9009\\u62E9\\u65B0\\u989C\\u8272\",\n      alphaLabel: \"\\u9009\\u62E9\\u900F\\u660E\\u5EA6\\u7684\\u503C\"\n    },\n    datepicker: {\n      now: \"\\u6B64\\u523B\",\n      today: \"\\u4ECA\\u5929\",\n      cancel: \"\\u53D6\\u6D88\",\n      clear: \"\\u6E05\\u7A7A\",\n      confirm: \"\\u786E\\u5B9A\",\n      dateTablePrompt: \"\\u4F7F\\u7528\\u65B9\\u5411\\u952E\\u4E0E Enter \\u952E\\u53EF\\u9009\\u62E9\\u65E5\\u671F\",\n      monthTablePrompt: \"\\u4F7F\\u7528\\u65B9\\u5411\\u952E\\u4E0E Enter \\u952E\\u53EF\\u9009\\u62E9\\u6708\\u4EFD\",\n      yearTablePrompt: \"\\u4F7F\\u7528\\u65B9\\u5411\\u952E\\u4E0E Enter \\u952E\\u53EF\\u9009\\u62E9\\u5E74\\u4EFD\",\n      selectedDate: \"\\u5DF2\\u9009\\u65E5\\u671F\",\n      selectDate: \"\\u9009\\u62E9\\u65E5\\u671F\",\n      selectTime: \"\\u9009\\u62E9\\u65F6\\u95F4\",\n      startDate: \"\\u5F00\\u59CB\\u65E5\\u671F\",\n      startTime: \"\\u5F00\\u59CB\\u65F6\\u95F4\",\n      endDate: \"\\u7ED3\\u675F\\u65E5\\u671F\",\n      endTime: \"\\u7ED3\\u675F\\u65F6\\u95F4\",\n      prevYear: \"\\u524D\\u4E00\\u5E74\",\n      nextYear: \"\\u540E\\u4E00\\u5E74\",\n      prevMonth: \"\\u4E0A\\u4E2A\\u6708\",\n      nextMonth: \"\\u4E0B\\u4E2A\\u6708\",\n      year: \"\\u5E74\",\n      month1: \"1 \\u6708\",\n      month2: \"2 \\u6708\",\n      month3: \"3 \\u6708\",\n      month4: \"4 \\u6708\",\n      month5: \"5 \\u6708\",\n      month6: \"6 \\u6708\",\n      month7: \"7 \\u6708\",\n      month8: \"8 \\u6708\",\n      month9: \"9 \\u6708\",\n      month10: \"10 \\u6708\",\n      month11: \"11 \\u6708\",\n      month12: \"12 \\u6708\",\n      weeks: {\n        sun: \"\\u65E5\",\n        mon: \"\\u4E00\",\n        tue: \"\\u4E8C\",\n        wed: \"\\u4E09\",\n        thu: \"\\u56DB\",\n        fri: \"\\u4E94\",\n        sat: \"\\u516D\"\n      },\n      weeksFull: {\n        sun: \"\\u661F\\u671F\\u65E5\",\n        mon: \"\\u661F\\u671F\\u4E00\",\n        tue: \"\\u661F\\u671F\\u4E8C\",\n        wed: \"\\u661F\\u671F\\u4E09\",\n        thu: \"\\u661F\\u671F\\u56DB\",\n        fri: \"\\u661F\\u671F\\u4E94\",\n        sat: \"\\u661F\\u671F\\u516D\"\n      },\n      months: {\n        jan: \"\\u4E00\\u6708\",\n        feb: \"\\u4E8C\\u6708\",\n        mar: \"\\u4E09\\u6708\",\n        apr: \"\\u56DB\\u6708\",\n        may: \"\\u4E94\\u6708\",\n        jun: \"\\u516D\\u6708\",\n        jul: \"\\u4E03\\u6708\",\n        aug: \"\\u516B\\u6708\",\n        sep: \"\\u4E5D\\u6708\",\n        oct: \"\\u5341\\u6708\",\n        nov: \"\\u5341\\u4E00\\u6708\",\n        dec: \"\\u5341\\u4E8C\\u6708\"\n      }\n    },\n    inputNumber: {\n      decrease: \"\\u51CF\\u5C11\\u6570\\u503C\",\n      increase: \"\\u589E\\u52A0\\u6570\\u503C\"\n    },\n    select: {\n      loading: \"\\u52A0\\u8F7D\\u4E2D\",\n      noMatch: \"\\u65E0\\u5339\\u914D\\u6570\\u636E\",\n      noData: \"\\u65E0\\u6570\\u636E\",\n      placeholder: \"\\u8BF7\\u9009\\u62E9\"\n    },\n    dropdown: {\n      toggleDropdown: \"\\u5207\\u6362\\u4E0B\\u62C9\\u9009\\u9879\"\n    },\n    mention: {\n      loading: \"\\u52A0\\u8F7D\\u4E2D\"\n    },\n    cascader: {\n      noMatch: \"\\u65E0\\u5339\\u914D\\u6570\\u636E\",\n      loading: \"\\u52A0\\u8F7D\\u4E2D\",\n      placeholder: \"\\u8BF7\\u9009\\u62E9\",\n      noData: \"\\u6682\\u65E0\\u6570\\u636E\"\n    },\n    pagination: {\n      goto: \"\\u524D\\u5F80\",\n      pagesize: \"\\u6761/\\u9875\",\n      total: \"\\u5171 {total} \\u6761\",\n      pageClassifier: \"\\u9875\",\n      page: \"\\u9875\",\n      prev: \"\\u4E0A\\u4E00\\u9875\",\n      next: \"\\u4E0B\\u4E00\\u9875\",\n      currentPage: \"\\u7B2C {pager} \\u9875\",\n      prevPages: \"\\u5411\\u524D {pager} \\u9875\",\n      nextPages: \"\\u5411\\u540E {pager} \\u9875\",\n      deprecationWarning: \"\\u4F60\\u4F7F\\u7528\\u4E86\\u4E00\\u4E9B\\u5DF2\\u88AB\\u5E9F\\u5F03\\u7684\\u7528\\u6CD5\\uFF0C\\u8BF7\\u53C2\\u8003 el-pagination \\u7684\\u5B98\\u65B9\\u6587\\u6863\"\n    },\n    dialog: {\n      close: \"\\u5173\\u95ED\\u6B64\\u5BF9\\u8BDD\\u6846\"\n    },\n    drawer: {\n      close: \"\\u5173\\u95ED\\u6B64\\u5BF9\\u8BDD\\u6846\"\n    },\n    messagebox: {\n      title: \"\\u63D0\\u793A\",\n      confirm: \"\\u786E\\u5B9A\",\n      cancel: \"\\u53D6\\u6D88\",\n      error: \"\\u8F93\\u5165\\u7684\\u6570\\u636E\\u4E0D\\u5408\\u6CD5!\",\n      close: \"\\u5173\\u95ED\\u6B64\\u5BF9\\u8BDD\\u6846\"\n    },\n    upload: {\n      deleteTip: \"\\u6309 Delete \\u952E\\u53EF\\u5220\\u9664\",\n      delete: \"\\u5220\\u9664\",\n      preview: \"\\u67E5\\u770B\\u56FE\\u7247\",\n      continue: \"\\u7EE7\\u7EED\\u4E0A\\u4F20\"\n    },\n    slider: {\n      defaultLabel: \"\\u6ED1\\u5757\\u4ECB\\u4E8E {min} \\u81F3 {max}\",\n      defaultRangeStartLabel: \"\\u9009\\u62E9\\u8D77\\u59CB\\u503C\",\n      defaultRangeEndLabel: \"\\u9009\\u62E9\\u7ED3\\u675F\\u503C\"\n    },\n    table: {\n      emptyText: \"\\u6682\\u65E0\\u6570\\u636E\",\n      confirmFilter: \"\\u7B5B\\u9009\",\n      resetFilter: \"\\u91CD\\u7F6E\",\n      clearFilter: \"\\u5168\\u90E8\",\n      sumText: \"\\u5408\\u8BA1\"\n    },\n    tour: {\n      next: \"\\u4E0B\\u4E00\\u6B65\",\n      previous: \"\\u4E0A\\u4E00\\u6B65\",\n      finish: \"\\u7ED3\\u675F\\u5BFC\\u89C8\"\n    },\n    tree: {\n      emptyText: \"\\u6682\\u65E0\\u6570\\u636E\"\n    },\n    transfer: {\n      noMatch: \"\\u65E0\\u5339\\u914D\\u6570\\u636E\",\n      noData: \"\\u65E0\\u6570\\u636E\",\n      titles: [\"\\u5217\\u8868 1\", \"\\u5217\\u8868 2\"],\n      filterPlaceholder: \"\\u8BF7\\u8F93\\u5165\\u641C\\u7D22\\u5185\\u5BB9\",\n      noCheckedFormat: \"\\u5171 {total} \\u9879\",\n      hasCheckedFormat: \"\\u5DF2\\u9009 {checked}/{total} \\u9879\"\n    },\n    image: {\n      error: \"\\u52A0\\u8F7D\\u5931\\u8D25\"\n    },\n    pageHeader: {\n      title: \"\\u8FD4\\u56DE\"\n    },\n    popconfirm: {\n      confirmButtonText: \"\\u786E\\u5B9A\",\n      cancelButtonText: \"\\u53D6\\u6D88\"\n    },\n    carousel: {\n      leftArrow: \"\\u4E0A\\u4E00\\u5F20\\u5E7B\\u706F\\u7247\",\n      rightArrow: \"\\u4E0B\\u4E00\\u5F20\\u5E7B\\u706F\\u7247\",\n      indicator: \"\\u5E7B\\u706F\\u7247\\u5207\\u6362\\u81F3\\u7D22\\u5F15 {index}\"\n    }\n  }\n};\n\nexport { zhCn as default };\n"], "mappings": ";;;AAEA,IAAI,OAAO;AAAA,EACT,MAAM;AAAA,EACN,IAAI;AAAA,IACF,YAAY;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,SAAS;AAAA,MACT,OAAO;AAAA,MACP,cAAc;AAAA,MACd,aAAa;AAAA,MACb,YAAY;AAAA,IACd;AAAA,IACA,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW;AAAA,MACX,WAAW;AAAA,MACX,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAAA,MACA,WAAW;AAAA,QACT,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAAA,MACA,QAAQ;AAAA,QACN,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAAA,IACF;AAAA,IACA,aAAa;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,IACZ;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,aAAa;AAAA,IACf;AAAA,IACA,UAAU;AAAA,MACR,gBAAgB;AAAA,IAClB;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,QAAQ;AAAA,IACV;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,UAAU;AAAA,MACV,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,WAAW;AAAA,MACX,WAAW;AAAA,MACX,oBAAoB;AAAA,IACtB;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,OAAO;AAAA,MACP,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IACA,QAAQ;AAAA,MACN,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,UAAU;AAAA,IACZ;AAAA,IACA,QAAQ;AAAA,MACN,cAAc;AAAA,MACd,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,IACxB;AAAA,IACA,OAAO;AAAA,MACL,WAAW;AAAA,MACX,eAAe;AAAA,MACf,aAAa;AAAA,MACb,aAAa;AAAA,MACb,SAAS;AAAA,IACX;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,UAAU;AAAA,MACV,QAAQ;AAAA,IACV;AAAA,IACA,MAAM;AAAA,MACJ,WAAW;AAAA,IACb;AAAA,IACA,UAAU;AAAA,MACR,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,QAAQ,CAAC,QAAkB,MAAgB;AAAA,MAC3C,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,IACpB;AAAA,IACA,OAAO;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,IACpB;AAAA,IACA,UAAU;AAAA,MACR,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,WAAW;AAAA,IACb;AAAA,EACF;AACF;", "names": []}